<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingdianjichi.practice.server.dao.SubjectMappingDao">

    <select id="getLabelSubjectCount" resultType="com.jingdianjichi.practice.server.entity.po.LabelCountPO">
        select
        count(distinct a.subject_id) as count,
        a.label_id as labelId
        from subject_mapping a,
        subject_info b
        where
        a.subject_id = b.id
        and a.is_deleted = 0
        and b.is_deleted = 0
        and a.category_id = #{categoryId}
        and b.subject_type in
        <foreach collection="subjectTypeList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        group by a.label_id
    </select>

    <select id="getLabelIdsBySubjectId"
            resultType="com.jingdianjichi.practice.server.entity.po.SubjectMappingPO">
        select distinct label_id as labelId
        from subject_mapping
        where is_deleted = 0
          and subject_id = #{subjectId,jdbcType=BIGINT}
    </select>


</mapper>

