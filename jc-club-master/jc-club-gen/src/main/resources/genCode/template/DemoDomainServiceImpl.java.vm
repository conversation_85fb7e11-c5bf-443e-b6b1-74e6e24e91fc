package com.jingdianjichi.${module}.domain.service.impl;

import com.jingdianjichi.${module}.common.enums.IsDeletedFlagEnum;
import com.jingdianjichi.${module}.domain.convert.${modelName}BOConverter;
import com.jingdianjichi.${module}.domain.entity.${modelName}BO;
import com.jingdianjichi.${module}.domain.service.${modelName}DomainService;
import com.jingdianjichi.${module}.infra.basic.entity.${modelName};
import com.jingdianjichi.${module}.infra.basic.service.${modelName}Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * ${tableComment} 领域service实现了
 *
 * <AUTHOR>
 * @since ${genDate}
 */
@Service
@Slf4j
public class ${modelName}DomainServiceImpl implements ${modelName}DomainService {

    @Resource
    private ${modelName}Service ${_modelName}Service;

    @Override
    public Boolean add(${modelName}BO ${_modelName}BO) {
        ${modelName} ${_modelName} = ${modelName}BOConverter.INSTANCE.convertBOToEntity(${_modelName}BO);
        ${_modelName}.setIsDeleted(IsDeletedFlagEnum.UN_DELETED.getCode());
        return ${_modelName}Service.insert(${_modelName}) > 0;
    }

    @Override
    public Boolean update(${modelName}BO ${_modelName}BO) {
        ${modelName} ${_modelName} = ${modelName}BOConverter.INSTANCE.convertBOToEntity(${_modelName}BO);
        return ${_modelName}Service.update(${_modelName}) > 0;
    }

    @Override
    public Boolean delete(${modelName}BO ${_modelName}BO) {
        ${modelName} ${_modelName} = new ${modelName}();
        ${_modelName}.setId(${_modelName}BO.getId());
        ${_modelName}.setIsDeleted(IsDeletedFlagEnum.DELETED.getCode());
        return ${_modelName}Service.update(${_modelName}) > 0;
    }

}
