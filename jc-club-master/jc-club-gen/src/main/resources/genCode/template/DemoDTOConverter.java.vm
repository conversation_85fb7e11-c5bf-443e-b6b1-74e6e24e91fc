package com.jingdianjichi.${module}.application.convert;

import com.jingdianjichi.${module}.application.dto.${modelName}DTO;
import com.jingdianjichi.${module}.domain.entity.${modelName}BO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * ${tableComment} dto转换器
 *
 * <AUTHOR>
 * @since ${genDate}
 */
@Mapper
public interface ${modelName}DTOConverter {

    ${modelName}DTOConverter INSTANCE = Mappers.getMapper(${modelName}DTOConverter.class);

    ${modelName}BO convertDTOToBO(${modelName}DTO ${_modelName}DTO);

}
