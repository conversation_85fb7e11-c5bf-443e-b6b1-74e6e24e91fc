package com.jingdianjichi.${module}.infra.basic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jingdianjichi.${module}.infra.basic.entity.${modelName};
import com.jingdianjichi.${module}.infra.basic.mapper.${modelName}Dao;
import com.jingdianjichi.${module}.infra.basic.service.${modelName}Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * ${tableComment} 表服务实现类
 *
 * <AUTHOR>
 * @since ${genDate}
 */
@Service("${modelName}Service")
public class ${modelName}ServiceImpl implements ${modelName}Service {

    @Resource
    private ${modelName}Dao ${_modelName}Dao;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    @Override
    public ${modelName} queryById(Long id) {
        return this.${_modelName}Dao.selectById(id);
    }

    /**
     * 新增数据
     *
     * @param ${_modelName} 实例对象
     * @return 实例对象
     */
    @Override
    public int insert(${modelName} ${_modelName}) {
        return this.${_modelName}Dao.insert(${_modelName});
    }

    /**
     * 修改数据
     *
     * @param ${_modelName} 实例对象
     * @return 实例对象
     */
    @Override
    public int update(${modelName} ${_modelName}) {
        return this.${_modelName}Dao.updateById(${_modelName});
    }

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    @Override
    public boolean deleteById(Long id) {
        return this.${_modelName}Dao.deleteById(id) > 0;
    }

    /**
     * 条件查询
     *
     * @param ${_modelName} 条件
     * @return 实例对象
     */
    @Override
    public ${modelName} queryByCondition(${modelName} ${_modelName}) {

        LambdaQueryWrapper<${modelName}> queryWrapper = Wrappers.<${modelName}>lambdaQuery()
        #foreach($field in $fields)
                .eq(Objects.nonNull(${_modelName}.get${field.upName}()), ${modelName}::get${field.upName}, ${_modelName}.get${field.upName}())
        #end
                ;
        return ${_modelName}Dao.selectOne(queryWrapper);

    }

}
